'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function JpgToHeic() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');

    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/jpg-to-heic');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/jpg-to-heic');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);

    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.jpg') && !lowerName.endsWith('.jpeg');
    });

    if (invalidFiles.length > 0) {
      setError('Please only upload JPG/JPEG files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg', '.JPG', '.JPEG']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload JPG/JPEG files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });

    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev =>
          prev.map(f =>
            f.id === fileItem.id
              ? { ...f, status: 'converting' }
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/jpg-to-heic', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'done', downloadUrl: result.url }
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'error', errorMessage: errorMessage }
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.jpg', '').replace('.jpeg', '').replace('.JPG', '').replace('.JPEG', '') + '.heic';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free JPG to HEIC Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert JPG photos to HEIC format online
        </p>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="mb-6 md:mb-8 relative">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive
                ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80'
                : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
              } shadow-sm hover:shadow-md`}
          >
            <input {...getInputProps()} accept=".jpg,.jpeg,.JPG,.JPEG" />
            <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
            <p className="text-lg md:text-xl mb-2 text-gray-800">Select JPG Photos</p>
            <p className="text-sm text-gray-600 mb-2">from your device</p>
            <p className="text-xs text-gray-500">Supports up to 100 files</p>

            {isMobile && (
              <div className="mt-4 text-sm text-indigo-600 font-medium">
                Tap here to select photos from your device
              </div>
            )}
          </div>
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
        </div>



        {files.length > 0 && (
          <div className="space-y-4 md:space-y-6">
            <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
              <table className="min-w-full table-fixed">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                    <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {files.map((file, index) => (
                    <tr key={file.id}>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                      <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                        <div className="truncate" title={file.name}>
                          {isMobile ?
                            file.name.length > 10 ?
                              file.name.slice(0, 7) + '...' + file.name.slice(-3)
                              : file.name
                            : file.name
                          }
                        </div>
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                        {file.status === 'done' && file.downloadUrl ? (
                          <a
                            href={file.downloadUrl}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            download
                          >
                            Download
                          </a>
                        ) : file.status === 'error' ? (
                          <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                        ) : file.status === 'converting' ? (
                          <span className="text-yellow-500 text-sm">Converting...</span>
                        ) : (
                          <span className="text-gray-500 text-sm">Waiting</span>
                        )}
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                        {file.status === 'done' ? (
                          <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                        ) : (
                          <button
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                            disabled={file.status === 'converting'}
                            title="Delete File"
                          >
                            <FiX className="w-5 h-5" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
              <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                  <select
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    value="HEIC"
                    disabled
                  >
                    <option>HEIC</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={quality}
                      onChange={(e) => setQuality(parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-600 w-12">{quality}%</span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={removeExif}
                    onChange={(e) => setRemoveExif(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Remove all EXIF information</span>
                </label>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={consentPrivacy}
                  onChange={(e) => setConsentPrivacy(e.target.checked)}
                  className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  required
                />
                <label className="text-sm text-gray-600">
                  I consent to heic-tojpg.com collecting and processing my data according to{' '}
                  <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                    Privacy Policy
                  </Link>
                  .
                </label>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                <button
                  onClick={handleClearAll}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                  disabled={isConverting}
                >
                  Clear all
                </button>
                <button
                  onClick={handleConvert}
                  disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {isConverting ? 'Converting...' : 'Convert'}
                </button>
                <button
                  onClick={handleDownloadAll}
                  disabled={!files.some(f => f.status === 'done')}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                >
                  <FiDownload className="w-4 h-4" />
                  <span>Download all</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Share buttons */}
        <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
          <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
          <div className="sharethis-inline-share-buttons"></div>
        </div>
        {/* Related tools */}
        <RelatedTools currentTool="JPG to HEIC" />

        {/* Conversion progress indicator */}
        {isConverting && isMobile && (
          <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
            <div className="text-center text-sm text-indigo-800 font-medium">
              Converting: {convertProgress.current} of {convertProgress.total} files
            </div>
            <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300"
                style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
              />
            </div>
          </div>
        )}

        <div className="mt-12 space-y-16">
          <section className="introduction">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">
              <a href="/jpg-to-heic" className="hover:text-indigo-600 transition-colors">JPG to HEIC</a> Converter Features
            </h2>
            <div className="space-y-16">
              {/* Feature Group 1: Free & Easy + Fast Conversion */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                      Professional JPG to HEIC Conversion - 100% Free
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>No registration or payment required - completely free tool to convert JPG to HEIC with enterprise-grade quality</li>
                      <li>Intuitive drag-and-drop interface with one-click upload functionality for efficient batch processing</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                      High-Speed JPG to HEIC Conversion with Advanced Compression
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Using HEIF/HEVC codec technology for superior image compression while preserving visual quality</li>
                      <li>Maintains color accuracy with ICC profile preservation and intelligent chroma subsampling</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/jpg-to-heic-converter-tool.webp"
                    alt="Professional JPG to HEIC Converter Tool"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 2: Security & Privacy */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2018/01/17/20/22/analytics-3088958_1280.jpg"
                    alt="Online Convert JPG to HEIC Format Converter"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                      Watermark-Free & Unlimited JPG to HEIC Converter
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Converted HEIC files are completely watermark-free, ready for professional photography projects, Apple devices, or multimedia applications</li>
                      <li>No file size limits, no quantity restrictions - change JPG to HEIC anytime, anywhere</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                      JPG to HEIC – End-to-End Encryption & Privacy Protection
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Employing AES-256 bit encryption standards to ensure JPG file security during transmission and processing</li>
                      <li>Using convert-and-delete technology - files are immediately removed from servers after jpg convert to heic operation</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Feature Group 3: Batch Processing & Compatibility */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                      Efficient Batch JPG to HEIC Conversion Technology
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Multi-threaded processing technology to simultaneously convert multiple JPG files to HEIC format</li>
                      <li>Perfect for photographers and iOS developers who need to convert JPG to HEIC free for Apple ecosystem compatibility</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                      Cross-Platform JPG to HEIC Conversion Compatibility
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>JPG to HEIC converter supports all major browsers including Chrome, Firefox, Edge, Safari and more</li>
                      <li>Compatible with Windows, macOS, Linux, Android, and iOS devices - a truly universal JPG to HEIC converter</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/03/27/18/54/technology-1283624_1280.jpg"
                    alt="Batch JPG to HEIC Conversion Compatibility"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 4: Quality Control & Online Access */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/11/29/06/18/home-office-1867761_1280.jpg"
                    alt="Online JPG to HEIC Quality Control"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                      Professional JPG to HEIC Converter with Advanced Image Processing
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Optimized HEIC compression parameters, balancing image quality and file size for professional results</li>
                      <li>Supports color profile management and bitrate control for precise visual fidelity when you convert JPG to HEIC</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                      Cloud-Based JPG to HEIC Conversion - No Software Installation Required
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Pure cloud processing - no software or plugins needed to convert JPG to HEIC free</li>
                      <li>WebAssembly optimization technology for efficient browser-based processing on any device</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="what-is">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Understanding the JPG to HEIC Converter</h2>

            <div className="space-y-16">
              {/* JPG Introduction Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is JPG to HEIC Conversion?</h3>
                    <p className="text-gray-600">
                      JPG to HEIC conversion is the process of transforming standard JPEG format images into Apple's High Efficiency Image Container (HEIC) format. While JPG is universally compatible, HEIC offers superior compression using the HEVC codec technology,
                      allowing for smaller file sizes without sacrificing quality. Our JPG to HEIC converter ensures optimal compression efficiency during conversion.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is the HEIC Format?</h3>
                    <p className="text-gray-600">
                      HEIC (High Efficiency Image Container) is a modern image format based on the HEIF (High Efficiency Image File Format) standard that provides superior compression for digital images. It uses the HEVC (H.265) video compression standard,
                      achieving file sizes up to 50% smaller than comparable JPG files while maintaining better quality. Despite these advantages, specialized tools are needed to convert JPG to HEIC for Apple ecosystem compatibility. You can visit: <a href="https://en.wikipedia.org/wiki/High_Efficiency_Image_File_Format" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on HEIF/HEIC</a>.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/what-is-heif-format.jpg"
                    alt="Professional Analysis of HEIC Format"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* JPG Details Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://image.heic-tojpg.com/what-is-jpeg.jpg"
                    alt="Detailed Explanation of JPG Files"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is a JPG File?</h3>
                    <p className="text-gray-600">
                      JPG (or JPEG - Joint Photographic Experts Group) is a raster image file format that uses lossy compression to reduce file size. It employs the discrete cosine transform (DCT) algorithm and can display up to 16.7 million colors.
                      JPG files are widely supported across all platforms and applications, but for users looking to benefit from better compression, using our tool to convert JPG to HEIC free is increasingly popular. You can visit: <a href="https://en.wikipedia.org/wiki/JPEG" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on JPEG</a>.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is Metadata in Image Files?</h3>
                    <p className="text-gray-600">
                      Image metadata includes information about the file such as creation date, camera settings, device information, and sometimes location data. When using our JPG to HEIC converter,
                      you can choose to preserve or remove this metadata during the conversion process. Preserving metadata is valuable for photographers while removing it can enhance privacy when you change JPG to HEIC format.
                    </p>
                  </div>
                </div>
              </div>

              {/* Format Comparison Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">JPG vs. HEIC: Understanding the Differences</h3>
                    <p className="text-gray-600">
                      While JPG offers universal compatibility using DCT-based compression, HEIC provides superior efficiency using the HEVC codec. HEIC files can be up to 50% smaller than JPGs while maintaining the same visual quality, offering better bit depth support, and wider color gamut.
                      Our JPG to HEIC converter enables users to benefit from these advanced features while ensuring optimal compression quality.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Which Image Formats Can I Upload?</h3>
                    <p className="text-gray-600">
                      Our JPG to HEIC converter primarily supports JPEG/JPG files (.jpg/.jpeg extensions). This specialized tool is designed to efficiently convert JPG to HEIC format while preserving all image attributes including color profiles and image quality.
                      The conversion process utilizes advanced compression algorithms and rate-distortion optimization to ensure optimal results.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/metadata-in-image.webp"
                    alt="JPG vs HEIC Format Comparison"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Conversion Benefits Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2015/01/08/18/27/startup-593341_1280.jpg"
                    alt="Benefits of JPG to HEIC Conversion"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Why Convert JPG to HEIC?</h3>
                    <p className="text-gray-600">
                      Converting JPG to HEIC offers significant storage savings while maintaining or improving image quality. HEIC files occupy approximately half the storage space of equivalent JPG files, making them ideal for mobile devices and cloud storage.
                      Our JPG to HEIC converter provides an efficient way to manage storage limitations while preserving visual fidelity across Apple's ecosystem.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Benefits of HEIC Format</h3>
                    <p className="text-gray-600">
                      HEIC files offer several technical advantages including better compression efficiency, support for 16-bit color depth, transparency, and the ability to store multiple images in a single file. When you convert jpg to heic,
                      you gain access to these benefits plus better HDR support, making HEIC an excellent format for modern photography workflows, especially within the Apple ecosystem.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="how-to">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Use the JPG to HEIC Converter</h2>

            <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
              <div>
                <ol className="space-y-6 relative">
                  <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload JPG Files</h3>
                    <p className="text-gray-600">
                      Drag and drop your JPG files into the conversion area, or click to select files from your device. Our JPG to HEIC converter supports batch uploading of multiple files for simultaneous processing, increasing efficiency.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Choose Conversion Settings</h3>
                    <p className="text-gray-600">
                      Adjust JPG to HEIC converter settings to optimize your output. You can select quality levels and choose to preserve or remove metadata from your images for enhanced privacy or authenticity when converting from JPG to HEIC format.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Convert and Download</h3>
                    <p className="text-gray-600">
                      Click the "Convert" button to start the JPG to HEIC conversion process. Once completed, you can download HEIC files individually or use our batch download option to download all converted files at once.
                    </p>
                  </li>
                </ol>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2015/05/31/10/55/man-791049_1280.jpg"
                  alt="JPG to HEIC Conversion Process"
                  className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                />
              </div>
            </div>
          </section>
        </div>


        <section className="why-use mb-8 mt-12">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">Why Choose Our JPG to HEIC Converter</h2>

          <div className="space-y-16">
            {/* Reason 1 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Superior Compression Technology</h3>
                <p className="text-gray-600">
                  JPG has been the standard format for decades, but HEIC represents the next generation in image compression. Our JPG to HEIC converter leverages the HEVC codec to provide files that are up to 50% smaller while maintaining or even improving visual quality.
                  The conversion process uses advanced quantization tables and entropy encoding to optimize each image.
                </p>
                <p className="text-gray-600">
                  Our JPG to HEIC converter maintains optimal image quality while changing the file format, using advanced bit depth preservation and chroma subsampling techniques to provide the highest fidelity conversion possible.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg"
                  alt="JPG to HEIC Superior Compression"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 2 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg"
                  alt="Simplified JPG to HEIC Workflow"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Apple Ecosystem Integration</h3>
                <p className="text-gray-600">
                  For users in the Apple ecosystem, having images in HEIC format ensures better compatibility with iOS, macOS, and iPadOS. Converting JPG to HEIC allows for smoother workflows when sharing and editing images across Apple devices,
                  especially when working with Photos app, iCloud, and other native applications.
                </p>
                <p className="text-gray-600">
                  Our JPG to HEIC converter's batch processing feature allows you to convert jpg to heic files simultaneously, supporting parallel multi-task processing that saves valuable time and storage space in your photography or development workflow.
                </p>
              </div>
            </div>

            {/* Reason 3 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Privacy and Security</h3>
                <p className="text-gray-600">
                  Using our JPG to HEIC converter tool, you can choose to remove or preserve metadata from your images during the conversion process. This feature is particularly important for privacy-conscious users,
                  allowing you to control what information is retained when you convert JPG to HEIC free online.
                </p>
                <p className="text-gray-600">
                  Our secure conversion infrastructure employs TLS encryption and secure file handling protocols, ensuring your images remain private throughout the JPG to HEIC conversion process. All uploaded files are automatically deleted after processing,
                  providing peace of mind for security-conscious users.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2017/10/10/21/46/laptop-2838917_1280.jpg"
                  alt="JPG to HEIC Privacy Protection"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 4 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://image.heic-tojpg.com/jfif-to-jpg-web-optimization.webp"
                  alt="JPG to HEIC Quality Preservation"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Professional Image Quality</h3>
                <p className="text-gray-600">
                  Our JPG to HEIC converter uses advanced image processing algorithms to ensure the highest quality output. The conversion process leverages HEVC's improved color sampling and encoding efficiency,
                  making it ideal for professional photographers, iOS developers, and digital artists who need to change JPG to HEIC without quality loss.
                </p>
                <p className="text-gray-600">
                  The HEIC format's superior compression ensures that image quality is maintained or even improved when you convert JPG to HEIC, making it perfect for archival photography, professional portfolios, and high-quality image libraries.
                </p>
              </div>
            </div>
          </div>
        </section>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Frequently Asked Questions About JPG to HEIC Conversion</h2>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900">What's the difference between JPG and HEIC?</h3>
              <p className="mt-1 text-gray-700">
                JPG is an older format that uses discrete cosine transform for lossy compression, while HEIC is a modern format developed by the MPEG group that uses HEVC (H.265) compression algorithms. 
                While JPG files are universally supported, HEIC files are typically 40-50% smaller at equivalent visual quality, which is why many users seek to convert JPG to HEIC for storage efficiency.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Will I lose quality when converting JPG to HEIC?</h3>
              <p className="mt-1 text-gray-700">
                When converting from JPG to HEIC using our converter, there is typically no quality loss and often an improvement in quality-to-size ratio. HEIC's advanced compression algorithms actually allow for better quality retention than JPG at equivalent file sizes.
                Our JPG to HEIC converter ensures optimal image fidelity through advanced processing algorithms and intelligent quantization techniques.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Is it safe to convert JPG to HEIC online?</h3>
              <p className="mt-1 text-gray-700">
                Yes, our online JPG to HEIC converter follows strict security protocols when handling all files. Your images are briefly processed on our secure servers and then automatically deleted.
                We don't permanently store your uploaded files or use them for any other purpose. All conversion processes take place in a TLS-encrypted secure environment, ensuring your jpg convert to heic
                process is completely safe and reliable.
              </p>
            </div>
          </div>
        </div>
      </main>

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
          aria-label="Scroll to top"
        >
          <FiArrowUp className="w-6 h-6" />
        </button>
      )}
    </>
  );
} 